"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, MapPin, Filter } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"

export function HeroSection() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [practiceArea, setPracticeArea] = useState("")
  const [location, setLocation] = useState("")

  const handleSearch = () => {
    const params = new URLSearchParams()
    if (searchTerm) params.set("q", searchTerm)
    if (location) {
      // Try to parse location as "City, State" format
      const locationParts = location.split(',').map(part => part.trim())
      if (locationParts.length >= 2) {
        params.set("city", locationParts[0])
        params.set("state", locationParts[1])
      } else {
        // If not in "City, State" format, treat as city
        params.set("city", location)
      }
    }

    router.push(`/search?${params.toString()}`)
  }

  return (
    <section className="relative bg-gradient-to-br from-primary to-primary/90 text-primary-foreground py-20">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="relative container mx-auto px-4 text-center">
        <h1 className="text-4xl md:text-6xl font-bold mb-4 text-balance">Find Trusted Car Accident Lawyers</h1>
        <p className="text-xl md:text-2xl mb-8 text-primary-foreground/90 text-pretty">
          Get expert legal representation when you need it most
        </p>

        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by name or specialty..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={practiceArea} onValueChange={setPracticeArea}>
              <SelectTrigger>
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Practice Area" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="car-accident">Car Accidents</SelectItem>
                <SelectItem value="personal-injury">Personal Injury</SelectItem>
                <SelectItem value="truck-accident">Truck Accidents</SelectItem>
                <SelectItem value="motorcycle">Motorcycle Accidents</SelectItem>
              </SelectContent>
            </Select>

            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="City, State"
                className="pl-10"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>

            <Button className="bg-accent hover:bg-accent/90 text-accent-foreground" onClick={handleSearch}>
              Search Lawyers
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
