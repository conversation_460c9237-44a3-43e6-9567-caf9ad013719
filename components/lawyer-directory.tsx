"use client"

import { useState, useEffect } from "react"
import { LawyerCard } from "@/components/lawyer-card"
import { PaginationComponent } from "@/components/pagination"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getAttorne<PERSON>, getStates, Attorney } from "@/lib/supabase"
import { getRecordsPerPage } from "@/lib/utils/pagination"
import { useSearchParams } from "next/navigation"

export function LawyerDirectory() {
  const [attorneys, setAttorneys] = useState<Attorney[]>([])
  const [states, setStates] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState("rating")
  const [filterBy, setFilterBy] = useState("all")
  const [totalItems, setTotalItems] = useState(0)
  const searchParams = useSearchParams()

  // Parse page from URL params, default to 1
  const currentPage = Math.max(1, parseInt(searchParams?.get('page') || '1', 10))
  const recordsPerPage = getRecordsPerPage()

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        setError(null) // Clear previous errors
        
        console.log(`Fetching attorneys - Page: ${currentPage}, Records per page: ${recordsPerPage}`)
        
        const [attorneysResult, statesResult] = await Promise.all([
          getAttorneys(currentPage, recordsPerPage),
          getStates()
        ])

        console.log('Attorneys result:', attorneysResult)
        console.log('States result:', statesResult)

        if (attorneysResult.error) {
          console.error('Attorney fetch error:', attorneysResult.error)
          setError(attorneysResult.error)
        } else {
          console.log(`Loaded ${attorneysResult.attorneys.length} attorneys out of ${attorneysResult.total} total`)
          setAttorneys(attorneysResult.attorneys)
          setTotalItems(attorneysResult.total)
        }

        if (statesResult.error) {
          console.error('States fetch error:', statesResult.error)
        } else {
          setStates(statesResult.states)
        }
      } catch (err) {
        console.error('Unexpected error fetching data:', err)
        setError('Failed to load attorneys. Please check your connection.')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentPage, recordsPerPage])

  const filteredAttorneys = attorneys.filter((attorney) => {
    if (filterBy === "all") return true
    return attorney.state.toLowerCase() === filterBy.toLowerCase()
  })

  const sortedAttorneys = [...filteredAttorneys].sort((a, b) => {
    switch (sortBy) {
      case "rating":
        return b.total_score - a.total_score
      case "reviews":
        return b.reviews_count - a.reviews_count
      case "name":
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  if (loading) {
    return (
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-lg">Loading attorneys...</p>
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-lg text-destructive">Error: {error}</p>
          </div>
        </div>
      </section>
    )
  }
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-3xl font-bold mb-2">Top Car Accident Lawyers</h2>
            <p className="text-muted-foreground">{filteredAttorneys.length} lawyers found</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 mt-4 md:mt-0">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="reviews">Most Reviews</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by state" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                {states.map((state) => (
                  <SelectItem key={state} value={state.toLowerCase()}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedAttorneys.map((attorney) => (
            <LawyerCard key={attorney.source_id} lawyer={attorney} />
          ))}
        </div>

        {sortedAttorneys.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground text-lg">No lawyers found matching your criteria.</p>
            <Button variant="outline" className="mt-4 bg-transparent" onClick={() => setFilterBy("all")}>
              Clear Filters
            </Button>
          </div>
        )}

        {/* Pagination */}
        {sortedAttorneys.length > 0 && totalItems > recordsPerPage && (
          <div className="mt-8">
            <PaginationComponent
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / recordsPerPage)}
              baseUrl="/"
            />
          </div>
        )}
      </div>
    </section>
  )
}
