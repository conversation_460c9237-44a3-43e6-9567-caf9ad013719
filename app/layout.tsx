import type { Metadata } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { Analytics } from '@vercel/analytics/next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Find Car Accident Attorneys | Top-Rated Lawyers Directory',
  description: 'Find the best car accident attorneys in your area. Browse verified reviews, ratings, and contact information for top-rated personal injury lawyers.',
  keywords: 'car accident attorney, personal injury lawyer, auto accident lawyer, car crash attorney, injury lawyer',
  authors: [{ name: 'Find Car Accident Attorneys' }],
  creator: 'Find Car Accident Attorneys',
  publisher: 'Find Car Accident Attorneys',
  metadataBase: new URL('https://findcaraccidentattorneys.org'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Find Car Accident Attorneys | Top-Rated Lawyers Directory',
    description: 'Find the best car accident attorneys in your area. Browse verified reviews, ratings, and contact information for top-rated personal injury lawyers.',
    url: 'https://findcaraccidentattorneys.org',
    siteName: 'Find Car Accident Attorneys',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Find Car Accident Attorneys | Top-Rated Lawyers Directory',
    description: 'Find the best car accident attorneys in your area. Browse verified reviews, ratings, and contact information for top-rated personal injury lawyers.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
    generator: 'v0.app'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <script
          defer
          src="https://web-umami-100125.vercel.app/script.js"
          data-website-id="900e5eda-3efd-4c2b-b943-6559c03afebe"
        />
      </head>
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
